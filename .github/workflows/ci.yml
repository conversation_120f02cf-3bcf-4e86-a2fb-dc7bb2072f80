name: CI

on: [ push ]

jobs:
  test:
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.ref }}
      cancel-in-progress: true
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Validate Gradle wrapper
        uses: gradle/actions/wrapper-validation@v4

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Run linter
        run: >
          ./gradlew 
          ktlintCheck

      - name: Run the service
        run: |
          set -e
          make start &
          
          RETRY=60
          
          while [ $RETRY -gt 0 ]
          do
              EXIT_CODE=0
              nc -z localhost 8000 || EXIT_CODE=$?
              if [ $EXIT_CODE -eq 0 ]
              then
                  break
              else
                  echo "Retrying Again" >&2
          
                  let RETRY-=1
                  sleep 5
              fi
          done
          
          make stop
          
          if [ $EXIT_CODE -ne 0 ]
          then
              echo "Failed to start the service" >&2
          fi
          
          exit $EXIT_CODE

      - name: Run tests
        run: >
          ./gradlew 
          test
          koverXmlReport

      - name: Publish Test Report
        uses: mikepenz/action-junit-report@9379f0ccddcab154835d4e2487555ee79614fe95
        if: always() # always run even if the previous step fails
        with:
          check_name: JUnit Test Report
          report_paths: '**/build/test-results/**/TEST-*.xml'
          annotate_notice: false
